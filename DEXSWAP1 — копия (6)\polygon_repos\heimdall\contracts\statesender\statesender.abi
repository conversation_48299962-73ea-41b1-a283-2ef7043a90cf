[{"constant": false, "inputs": [{"name": "receiver", "type": "address"}, {"name": "data", "type": "bytes"}], "name": "syncState", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "counter", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "renounceOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "isOwner", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "address"}], "name": "registrations", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "sender", "type": "address"}, {"name": "receiver", "type": "address"}], "name": "register", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "user", "type": "address"}, {"indexed": true, "name": "sender", "type": "address"}, {"indexed": true, "name": "receiver", "type": "address"}], "name": "NewRegistration", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "id", "type": "uint256"}, {"indexed": true, "name": "contractAddress", "type": "address"}, {"indexed": false, "name": "data", "type": "bytes"}], "name": "StateSynced", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "previousOwner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}]