package tx

import (
	"github.com/gorilla/mux"

	"github.com/cosmos/cosmos-sdk/client/context"
)

// RegisterRoutes registers REST routes
func RegisterRoutes(cliCtx context.CLIContext, r *mux.Router) {
	r.<PERSON>le<PERSON>unc("/txs/{hash}", QueryTxRequestHandlerFn(cliCtx)).Methods("GET")
	r.<PERSON>le<PERSON>un<PERSON>("/txs/{hash}/commit-proof", QueryCommitTxRequestHandlerFn(cliCtx)).Methods("GET")
	r.HandleFunc("/txs/{hash}/side-tx", QuerySideTxRequestHandlerFn(cliCtx)).Methods("GET")
	r.<PERSON>le<PERSON>("/txs", QueryTxsRequestHandlerFn(cliCtx)).Methods("GET")
	r.Handle<PERSON>unc("/txs", BroadcastTxRequest(cliCtx)).Methods("POST")
	r.<PERSON>le<PERSON>unc("/txs/encode", EncodeTxRequestHandlerFn(cliCtx)).Methods("POST")
}
